# 普通话训练页面数据源重构需求

## 背景

当前 `src/pages/speak/putonghua-training.vue` 页面的数据获取依赖于传统接口请求，存在以下问题：
- 数据管理分散，难以统一维护和更新
- 训练内容的一致性和可控性不足
- 无法灵活定制和扩展训练内容
- 缺乏统一的数据管理后台

为了提升数据管理效率，实现训练内容的统一管理和动态更新，需要将数据源重构为云数据库存储方案，通过 uniCloud 云对象提供高效的数据访问接口。

## 需求

### 功能需求

#### 1. 数据源重构
- **云数据库存储**：将汉字训练数据存储在云端数据库的 `putonghua` 表中，实现统一的数据管理
- **数据结构标准化**：每条数据记录包含汉字的完整训练信息，遵循统一的数据结构规范
- **云对象接口**：在 `uniCloud-aliyun/cloudfunctions/speak/index.obj.js` 中添加数据读取函数
- **动态数据查询**：页面启动时通过云对象调用从 `putonghua` 表中查询所有训练数据
- **动态分类生成**：从云数据库查询结果中自动收集 `category` 字段值并去重后动态生成分类列表
- **分类展示**：根据动态生成的分类信息在"我的字库"区域按分类显示汉字

#### 2. 页面功能调整
- **字库展示优化**：保持现有 UI 样式，按动态生成的分类显示汉字
- **内容随机选择**：点击汉字时从对应的云数据库记录中随机选择训练内容
- **交互逻辑保持**：维持现有的页面布局和用户交互方式
- **云对象调用**：通过 uniCloud 云对象方式调用云函数获取训练数据

#### 3. 数据库表结构规范
`putonghua` 表中每条记录应包含：
- 汉字本身（character 字段）
- 分类信息（category 字段，用于动态分类生成）
- 相关词语及其拼音（words 字段，JSON 数组格式）
- 每个词语关联的句子及其拼音（嵌套在 words 数组中）

### 非功能需求

#### 1. 性能要求
- 页面初始加载时间不超过 3 秒（包含一次性数据加载时间）
- 汉字切换响应时间不超过 100ms（本地数据操作）
- 词语和句子随机选择响应时间不超过 50ms（本地计算）
- 分类筛选操作响应时间不超过 200ms（本地数据筛选）
- 云函数响应时间不超过 1 秒
- 数据加载完成后，所有后续操作均为本地操作，无网络延迟

#### 2. 可维护性
- 数据文件结构清晰，易于扩展和维护
- 代码模块化，便于后续功能迭代
- 良好的错误处理机制

#### 3. 兼容性
- 使用全新的云数据库数据结构，不需要考虑与原有接口数据的兼容性
- 与录音练习功能无缝集成
- 支持现有的拼音显示/隐藏功能
- 兼容 uniCloud 云开发环境的各项服务

## 技术方案

### 实现思路

#### 1. 云数据库表结构设计
```javascript
// putonghua 表中的数据记录结构
{
  _id: "unique_id_123",
  character: "这",
  category: "翘舌音",
  words: [
    {
      text: "这个",
      pinyin: "zhè ge",
      sentences: [
        { text: "这是一个美好的日子。", pinyin: "zhè shì yí ge měi hǎo de rì zi." },
        { text: "这个想法很不错。", pinyin: "zhè ge xiǎng fǎ hěn bù cuò." }
      ]
    },
    {
      text: "这里",
      pinyin: "zhè lǐ",
      sentences: [
        { text: "这里的风景很美。", pinyin: "zhè lǐ de fēng jǐng hěn měi." },
        { text: "这里是我的家乡。", pinyin: "zhè lǐ shì wǒ de jiā xiāng." }
      ]
    },
    {
      text: "这样",
      pinyin: "zhè yàng",
      sentences: [
        { text: "这样做是对的。", pinyin: "zhè yàng zuò shì duì de." },
        { text: "这样的天气很舒服。", pinyin: "zhè yàng de tiān qì hěn shū fu." }
      ]
    }
  ],
  createTime: "2025-01-01T00:00:00.000Z",
  updateTime: "2025-01-01T00:00:00.000Z"
}
```

#### 2. 云对象接口设计
在 `uniCloud-aliyun/cloudfunctions/speak/index.obj.js` 中添加数据查询方法：
```javascript
// 云对象方法：获取所有普通话训练数据
async getPutonghuaData() {
  const db = uniCloud.database()
  const collection = db.collection('putonghua')

  try {
    const res = await collection.get()
    return {
      code: 0,
      message: 'success',
      data: res.data
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message,
      data: []
    }
  }
}
```

#### 3. 前端云对象调用和数据处理
```javascript
// 在 putonghua-training.vue 中调用云对象获取数据
const speakObj = uniCloud.importObject('speak')
let allPutonghuaData = [] // 本地缓存所有数据

// 页面初始化时一次性获取所有普通话训练数据
const loadPutonghuaData = async () => {
  try {
    const result = await speakObj.getPutonghuaData()
    if (result.code === 0) {
      allPutonghuaData = result.data // 缓存到本地
      return result.data
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('获取普通话数据失败：', error)
    uni.showToast({ title: '数据加载失败', icon: 'none' })
    return []
  }
}

// 根据汉字从本地缓存中获取训练数据
const getPutonghuaByCharacter = (character) => {
  return allPutonghuaData.find(item => item.character === character) || null
}

// 动态生成分类并按分类组织数据
const organizeByCategory = (dataList) => {
  const categories = {}

  // 自动收集所有分类并去重
  dataList.forEach(data => {
    const category = data.category
    if (!categories[category]) {
      categories[category] = []
    }
    categories[category].push(data.character)
  })

  // 对每个分类内的汉字进行排序
  Object.keys(categories).forEach(category => {
    categories[category].sort((a, b) => a.localeCompare(b, 'zh-Hans-CN'))
  })

  return categories
}

// 随机选择词语和句子（本地操作）
const getRandomContent = (character) => {
  const characterData = getPutonghuaByCharacter(character)
  if (!characterData || !characterData.words.length) return null

  // 随机选择一个词语
  const randomWord = characterData.words[Math.floor(Math.random() * characterData.words.length)]

  // 随机选择该词语的一个句子
  const randomSentence = randomWord.sentences.length > 0
    ? randomWord.sentences[Math.floor(Math.random() * randomWord.sentences.length)]
    : null

  return {
    character: characterData.character,
    word: randomWord,
    sentence: randomSentence
  }
}
```

### 架构设计

```mermaid
graph TD
    A[页面初始化] --> B[调用云对象speak]
    B --> C[查询putonghua表]
    C --> D[返回所有训练数据]
    D --> E[缓存到本地allPutonghuaData]
    E --> F[自动收集category字段]
    F --> G[去重生成分类列表]
    G --> H[构建字库分类结构]
    H --> I[渲染字库UI]

    J[用户点击汉字] --> K[从本地缓存获取数据]
    K --> L[本地随机选择词语]
    L --> M[本地随机选择句子]
    M --> N[更新页面显示]

    O[云数据库putonghua表] --> P[汉字记录]
    P --> Q[character字段]
    P --> R[category字段]
    P --> S[words数组字段]
    S --> T[每个词语的sentences数组]

    U[云对象speak] --> V[getPutonghuaData方法]

    W[本地数据处理] --> X[分类筛选]
    W --> Y[内容随机选择]
    W --> Z[数据查找匹配]
```

### 技术栈与约束

#### 技术栈
- **前端框架**: Vue.js 3 + Composition API
- **云开发平台**: uniCloud 阿里云版
- **数据存储**: uniCloud 云数据库
- **接口调用**: uniCloud 云对象
- **数据格式**: JSON 格式存储

#### 约束条件
- 单条数据记录大小不超过 16MB（MongoDB 限制）
- 云数据库读写操作遵循 uniCloud 配额限制
- 云对象调用频率需控制在合理范围内
- 支持 H5、小程序、App 多端运行
- 需要配置 uniCloud 服务空间和云数据库

#### 实现步骤
1. **云数据库表创建**：在 uniCloud 控制台创建 `putonghua` 表并设计索引
2. **数据导入**：将训练数据按照标准结构导入云数据库
3. **云对象开发**：在 `speak/index.obj.js` 中实现数据查询方法
4. **前端云对象调用**：修改页面中的数据获取逻辑，使用云对象调用
5. **动态分类实现**：实现从云数据库结果中自动收集分类的机制
6. **字库显示重构**：修改字库显示逻辑以支持云端数据和动态分类
7. **内容选择优化**：实现词语和句子的随机选择逻辑
8. **缓存机制实现**：添加本地缓存减少重复网络请求
9. **错误处理完善**：添加网络异常和数据异常的处理逻辑
10. **测试验证**：确保功能完整性和性能表现

## 风险评估

### 假设与未知因素

- **假设**：uniCloud 云数据库服务稳定可靠，响应时间在可接受范围内
- **假设**：云对象调用在所有目标平台上都能正常工作
- **未知因素**：不同网络环境下的数据加载性能差异
- **未知因素**：云数据库并发访问的性能表现
- **未知因素**：uniCloud 服务的长期稳定性和成本控制

### 潜在风险

#### 1. 技术风险
- **初始网络依赖性**：页面首次加载依赖网络连接获取数据，离线状态下无法初始化
- **云服务稳定性**：uniCloud 服务中断可能影响应用初始化
- **一次性数据量大**：所有数据一次性加载可能影响初始加载速度
- **内存占用**：全量数据缓存在内存中可能增加内存使用

**解决方案**：
- 实现本地持久化缓存，支持离线访问已缓存的数据
- 添加服务降级和重试机制
- 优化数据结构，减少不必要的数据字段
- 实现数据压缩和分页加载策略（如数据量过大）
- 一次加载后的所有操作均为本地操作，提升用户体验

#### 2. 数据管理风险
- **数据结构一致性**：云数据库中多条记录的格式可能不统一
- **分类命名规范**：不同数据记录中的 category 字段可能存在命名不一致
- **数据安全性**：云数据库的数据安全和访问权限控制
- **数据备份恢复**：云数据库数据的备份和恢复机制

**解决方案**：
- 建立数据库 schema 校验和数据格式验证机制
- 制定分类命名规范和标准词典
- 配置合适的数据库访问权限和安全规则
- 建立定期数据备份和恢复流程
- 提供数据导入导出和批量管理工具

#### 3. 用户体验风险
- **首次加载延迟**：一次性数据加载可能导致初始化时间增加
- **网络异常处理**：初始化时网络不稳定的用户体验问题
- **功能回退**：重构过程中可能暂时影响现有功能

**解决方案**：
- 实现加载状态提示和骨架屏显示
- 添加网络异常提示和重试机制
- 数据加载完成后，所有操作均为本地操作，响应速度极快
- 实现本地持久化存储，减少重复加载
- 采用分阶段重构，确保功能连续性
- 充分的测试验证和回滚预案
