# Todo 模块 API 说明文档

本文档详细说明了 Todo 模块中 8 个核心方法的使用规范，包括任务管理和项目管理功能。

## 目录

- [任务管理 API](#任务管理-api)
  - [getTasks - 获取任务列表](#gettasks---获取任务列表)
  - [createTask - 创建任务](#createtask---创建任务)
  - [updateTask - 更新任务](#updatetask---更新任务)
  - [deleteTask - 删除任务](#deletetask---删除任务)
- [项目管理 API](#项目管理-api)
  - [getProjects - 获取项目列表](#getprojects---获取项目列表)
  - [createProject - 创建项目](#createproject---创建项目)
  - [updateProject - 更新项目](#updateproject---更新项目)
  - [deleteProject - 删除项目](#deleteproject---删除项目)
- [通用响应格式](#通用响应格式)
- [错误码说明](#错误码说明)

---

## 任务管理 API

### getTasks - 获取任务列表

获取符合条件的任务列表，支持多种筛选条件。

#### 入参（输入参数）

| 参数名      | 类型    | 必填 | 说明                         | 示例值                                                  |
| ----------- | ------- | ---- | ---------------------------- | ------------------------------------------------------- |
| mode        | string  | 否   | 时间筛选模式                 | `"all"` / `"today"` / `"yesterday"` / `"recent_7_days"` |
| keyword     | string  | 否   | 关键词搜索（搜索标题和内容） | `"会议"`                                                |
| priority    | number  | 否   | 优先级筛选                   | `0`（最低）/ `1`（低）/ `3`（中）/ `5`（高）            |
| projectName | string  | 否   | 项目名称筛选                 | `"工作项目"`                                            |
| completed   | boolean | 否   | 完成状态筛选                 | `true`（已完成）/ `false`（未完成）                     |

#### 返回值（输出参数）

**成功响应：**

```json
{
  "errCode": null,
  "errMsg": "获取任务列表成功",
  "data": [
    {
      "id": "task_id_123",
      "title": "任务标题",
      "content": "任务详细内容",
      "status": 0,
      "priority": 3,
      "projectId": "project_id_456",
      "projectName": "工作项目",
      "projectColor": "#3498db",
      "tags": ["tag_id_1", "tag_id_2"],
      "startDate": "2024-01-15 09:00",
      "dueDate": "2024-01-20 18:00",
      "completedTime": null,
      "createdTime": "2024-01-10 10:30",
      "modifiedTime": "2024-01-12 14:20",
      "isAllDay": false,
      "reminder": "2024-01-20T08:00:00.000Z",
      "kind": "TEXT"
    }
  ]
}
```

**字段说明：**

- `id`: 任务唯一标识符
- `title`: 任务标题
- `content`: 任务详细内容
- `status`: 任务状态（0=活跃，2=已完成）
- `priority`: 优先级（0=最低，1=低，3=中，5=高）
- `projectId`: 所属项目 ID
- `projectName`: 所属项目名称（仅在任务有项目时显示）
- `projectColor`: 项目颜色（仅在任务有项目时显示）
- `tags`: 标签 ID 数组
- `startDate`: 开始时间（格式化后的可读时间）
- `dueDate`: 截止时间（格式化后的可读时间）
- `completedTime`: 完成时间
- `createdTime`: 创建时间
- `modifiedTime`: 修改时间
- `isAllDay`: 是否全天任务
- `reminder`: 提醒时间（ISO 格式）
- `kind`: 任务类型（TEXT/CHECKLIST/NOTE）

#### 使用示例

```javascript
// 获取所有任务
const allTasks = await todoTool.getTasks()

// 获取今天的任务
const todayTasks = await todoTool.getTasks({ mode: 'today' })

// 搜索包含"会议"关键词的任务
const meetingTasks = await todoTool.getTasks({ keyword: '会议' })

// 获取高优先级且未完成的任务
const highPriorityTasks = await todoTool.getTasks({
  priority: 5,
  completed: false,
})

// 获取特定项目的任务
const projectTasks = await todoTool.getTasks({
  projectName: '工作项目',
})
```

---

### createTask - 创建任务

创建一个新的任务。

#### 入参（输入参数）

| 参数名      | 类型    | 必填 | 说明                                  | 示例值                       |
| ----------- | ------- | ---- | ------------------------------------- | ---------------------------- |
| title       | string  | 是   | 任务标题                              | `"完成项目报告"`             |
| content     | string  | 否   | 任务详细内容                          | `"需要包含数据分析和结论"`   |
| priority    | number  | 否   | 优先级，默认为 0                      | `3`                          |
| projectName | string  | 否   | 项目名称（会自动查找对应项目 ID）     | `"工作项目"`                 |
| tagNames    | array   | 否   | 标签名称数组（会自动查找对应标签 ID） | `["重要", "紧急"]`           |
| startDate   | string  | 否   | 开始时间                              | `"2024-01-15T09:00:00.000Z"` |
| dueDate     | string  | 否   | 截止时间                              | `"2024-01-20T18:00:00.000Z"` |
| isAllDay    | boolean | 否   | 是否全天任务，默认为 false            | `false`                      |
| reminder    | string  | 否   | 提醒时间                              | `"2024-01-20T08:00:00.000Z"` |
| kind        | string  | 否   | 任务类型，默认为 TEXT                 | `"TEXT"`                     |

#### 返回值（输出参数）

**成功响应：**

```json
{
  "errCode": null,
  "errMsg": "任务创建成功",
  "data": {
    "id": "new_task_id_789",
    "title": "完成项目报告",
    "content": "需要包含数据分析和结论",
    "status": 0,
    "priority": 3,
    "projectId": "project_id_456",
    "tags": ["tag_id_1", "tag_id_2"],
    "startDate": "2024-01-15T09:00:00.000Z",
    "dueDate": "2024-01-20T18:00:00.000Z",
    "isAllDay": false,
    "reminder": "2024-01-20T08:00:00.000Z",
    "kind": "TEXT"
  }
}
```

#### 使用示例

```javascript
// 创建简单任务
const simpleTask = await todoTool.createTask({
  title: '买菜',
})

// 创建完整任务
const fullTask = await todoTool.createTask({
  title: '完成项目报告',
  content: '需要包含数据分析和结论',
  priority: 5,
  projectName: '工作项目',
  tagNames: ['重要', '紧急'],
  dueDate: '2024-01-20T18:00:00.000Z',
  reminder: '2024-01-20T08:00:00.000Z',
})
```

---

### updateTask - 更新任务

更新现有任务的信息。

#### 入参（输入参数）

| 参数名     | 类型   | 必填 | 说明         | 示例值          |
| ---------- | ------ | ---- | ------------ | --------------- |
| taskId     | string | 是   | 任务 ID      | `"task_id_123"` |
| updateData | object | 是   | 更新数据对象 | 见下方详细说明  |

**updateData 对象字段：**

| 字段名      | 类型    | 必填 | 说明             | 示例值                       |
| ----------- | ------- | ---- | ---------------- | ---------------------------- |
| title       | string  | 否   | 新的任务标题     | `"修改后的标题"`             |
| content     | string  | 否   | 新的任务内容     | `"修改后的内容"`             |
| priority    | number  | 否   | 新的优先级       | `5`                          |
| projectName | string  | 否   | 新的项目名称     | `"新项目"`                   |
| tagNames    | array   | 否   | 新的标签名称数组 | `["标签1", "标签2"]`         |
| startDate   | string  | 否   | 新的开始时间     | `"2024-01-16T09:00:00.000Z"` |
| dueDate     | string  | 否   | 新的截止时间     | `"2024-01-21T18:00:00.000Z"` |
| isAllDay    | boolean | 否   | 是否全天任务     | `true`                       |
| status      | number  | 否   | 任务状态         | `2`                          |
| reminder    | string  | 否   | 提醒时间         | `"2024-01-21T08:00:00.000Z"` |

#### 返回值（输出参数）

**成功响应：**

```json
{
  "errCode": null,
  "errMsg": "任务更新成功",
  "data": {
    "id": "task_id_123",
    "title": "修改后的标题",
    "content": "修改后的内容",
    "status": 2,
    "priority": 5,
    "projectId": "new_project_id",
    "tags": ["new_tag_id_1", "new_tag_id_2"],
    "startDate": "2024-01-16T09:00:00.000Z",
    "dueDate": "2024-01-21T18:00:00.000Z",
    "isAllDay": true,
    "reminder": "2024-01-21T08:00:00.000Z",
    "kind": "TEXT"
  }
}
```

#### 使用示例

```javascript
// 更新任务标题
const updateTitle = await todoTool.updateTask('task_id_123', {
  title: '新的任务标题',
})

// 标记任务为已完成
const completeTask = await todoTool.updateTask('task_id_123', {
  status: 2,
  completedTime: new Date().toISOString(),
})

// 更新多个字段
const updateMultiple = await todoTool.updateTask('task_id_123', {
  title: '修改后的标题',
  priority: 5,
  projectName: '新项目',
  dueDate: '2024-01-25T18:00:00.000Z',
})
```

---

### deleteTask - 删除任务

删除指定的任务。

#### 入参（输入参数）

| 参数名 | 类型   | 必填 | 说明            | 示例值          |
| ------ | ------ | ---- | --------------- | --------------- |
| taskId | string | 是   | 要删除的任务 ID | `"task_id_123"` |

#### 返回值（输出参数）

**成功响应：**

```json
{
  "errCode": null,
  "errMsg": "任务删除成功",
  "data": {
    "deleted": true,
    "taskId": "task_id_123"
  }
}
```

#### 使用示例

```javascript
// 删除任务
const deleteResult = await todoTool.deleteTask('task_id_123')

if (deleteResult.errCode === null) {
  console.log('任务删除成功')
} else {
  console.error('删除失败:', deleteResult.errMsg)
}
```

---

## 项目管理 API

### getProjects - 获取项目列表

获取符合条件的项目列表。

#### 入参（输入参数）

| 参数名        | 类型    | 必填 | 说明                               | 示例值   |
| ------------- | ------- | ---- | ---------------------------------- | -------- |
| keyword       | string  | 否   | 关键词搜索（搜索项目名称）         | `"工作"` |
| includeClosed | boolean | 否   | 是否包含已关闭的项目，默认为 false | `true`   |

#### 返回值（输出参数）

**成功响应：**

```json
{
  "errCode": null,
  "errMsg": "获取项目列表成功",
  "data": [
    {
      "id": "project_id_456",
      "name": "工作项目",
      "color": "#3498db",
      "kind": "TASK",
      "closed": false,
      "createdTime": "2024-01-01 10:00",
      "modifiedTime": "2024-01-10 15:30"
    }
  ]
}
```

**字段说明：**

- `id`: 项目唯一标识符
- `name`: 项目名称
- `color`: 项目颜色（十六进制颜色码）
- `kind`: 项目类型（TASK=任务项目，NOTE=笔记项目）
- `closed`: 是否已关闭
- `createdTime`: 创建时间（格式化后的可读时间）
- `modifiedTime`: 修改时间（格式化后的可读时间）

#### 使用示例

```javascript
// 获取所有活跃项目
const activeProjects = await todoTool.getProjects()

// 获取包含已关闭项目的所有项目
const allProjects = await todoTool.getProjects({
  includeClosed: true,
})

// 搜索包含"工作"关键词的项目
const workProjects = await todoTool.getProjects({
  keyword: '工作',
})
```

---

### createProject - 创建项目

创建一个新的项目。

#### 入参（输入参数）

| 参数名 | 类型   | 必填 | 说明                    | 示例值              |
| ------ | ------ | ---- | ----------------------- | ------------------- |
| name   | string | 是   | 项目名称                | `"新工作项目"`      |
| color  | string | 否   | 项目颜色，默认为#3498db | `"#e74c3c"`         |
| kind   | string | 否   | 项目类型，默认为 TASK   | `"TASK"` / `"NOTE"` |

#### 返回值（输出参数）

**成功响应：**

```json
{
  "errCode": null,
  "errMsg": "项目创建成功",
  "data": {
    "id": "new_project_id_789",
    "name": "新工作项目",
    "color": "#e74c3c",
    "kind": "TASK",
    "closed": false
  }
}
```

#### 使用示例

```javascript
// 创建简单项目
const simpleProject = await todoTool.createProject({
  name: '个人项目',
})

// 创建完整项目
const fullProject = await todoTool.createProject({
  name: '新工作项目',
  color: '#e74c3c',
  kind: 'TASK',
})
```

---

### updateProject - 更新项目

更新现有项目的信息。

#### 入参（输入参数）

| 参数名     | 类型   | 必填 | 说明         | 示例值             |
| ---------- | ------ | ---- | ------------ | ------------------ |
| projectId  | string | 是   | 项目 ID      | `"project_id_456"` |
| updateData | object | 是   | 更新数据对象 | 见下方详细说明     |

**updateData 对象字段：**

| 字段名 | 类型    | 必填 | 说明         | 示例值             |
| ------ | ------- | ---- | ------------ | ------------------ |
| name   | string  | 否   | 新的项目名称 | `"修改后的项目名"` |
| color  | string  | 否   | 新的项目颜色 | `"#2ecc71"`        |
| kind   | string  | 否   | 新的项目类型 | `"NOTE"`           |
| closed | boolean | 否   | 是否关闭项目 | `true`             |

#### 返回值（输出参数）

**成功响应：**

```json
{
  "errCode": null,
  "errMsg": "项目更新成功",
  "data": {
    "id": "project_id_456",
    "name": "修改后的项目名",
    "color": "#2ecc71",
    "kind": "NOTE",
    "closed": false
  }
}
```

#### 使用示例

```javascript
// 更新项目名称
const updateName = await todoTool.updateProject('project_id_456', {
  name: '新的项目名称',
})

// 关闭项目
const closeProject = await todoTool.updateProject('project_id_456', {
  closed: true,
})

// 更新多个字段
const updateMultiple = await todoTool.updateProject('project_id_456', {
  name: '修改后的项目名',
  color: '#2ecc71',
  kind: 'NOTE',
})
```

---

### deleteProject - 删除项目

删除指定的项目。

#### 入参（输入参数）

| 参数名    | 类型   | 必填 | 说明            | 示例值             |
| --------- | ------ | ---- | --------------- | ------------------ |
| projectId | string | 是   | 要删除的项目 ID | `"project_id_456"` |

#### 返回值（输出参数）

**成功响应：**

```json
{
  "errCode": null,
  "errMsg": "项目删除成功",
  "data": {
    "deleted": true,
    "projectId": "project_id_456"
  }
}
```

#### 使用示例

```javascript
// 删除项目
const deleteResult = await todoTool.deleteProject('project_id_456')

if (deleteResult.errCode === null) {
  console.log('项目删除成功')
} else {
  console.error('删除失败:', deleteResult.errMsg)
}
```

---

## 通用响应格式

所有 API 方法都遵循统一的响应格式：

### 成功响应

```json
{
  "errCode": null,
  "errMsg": "操作成功的描述信息",
  "data": "具体的返回数据"
}
```

### 错误响应

```json
{
  "errCode": "ERROR_CODE",
  "errMsg": "错误描述信息",
  "details": "详细错误信息（可选）"
}
```

---

## 错误码说明

| 错误码              | 说明           |
| ------------------- | -------------- |
| `PARAM_IS_NULL`     | 必需参数为空   |
| `PARAM_INVALID`     | 参数格式无效   |
| `UNAUTHORIZED`      | 未授权访问     |
| `LOGIN_ERROR`       | 登录失败       |
| `TOKEN_NOT_FOUND`   | 访问令牌未找到 |
| `NETWORK_ERROR`     | 网络连接错误   |
| `TIMEOUT_ERROR`     | 请求超时       |
| `API_ERROR`         | API 接口错误   |
| `PARSE_ERROR`       | 数据解析错误   |
| `DATA_NOT_FOUND`    | 数据未找到     |
| `PROJECT_NOT_FOUND` | 项目不存在     |
| `TASK_NOT_FOUND`    | 任务不存在     |
| `TAG_NOT_FOUND`     | 标签不存在     |
| `UNKNOWN_ERROR`     | 未知错误       |

---

## 注意事项

1. **认证要求**：所有 API 调用前需要确保已通过认证（调用`initWithToken`或`login`方法）
2. **日期格式**：输入日期时建议使用 ISO 8601 格式（如：`2024-01-20T18:00:00.000Z`）
3. **项目和标签查找**：在创建和更新任务时，可以使用项目名称和标签名称，系统会自动查找对应的 ID
4. **数据简化**：返回的数据已经过简化处理，时间字段已格式化为可读格式
5. **错误处理**：建议在调用 API 后检查`errCode`字段来判断操作是否成功
6. **参数验证**：必填参数不能为空字符串或 null 值
7. **异步操作**：所有方法都是异步的，需要使用`await`或`.then()`来处理结果

---

_本文档基于 Todo 模块 v1.0 版本编写，如有更新请参考最新版本的代码实现。_
