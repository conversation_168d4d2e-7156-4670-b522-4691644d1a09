/**
 * 执行计划生成模块
 *
 * 主要功能：
 * 1. 根据用户输入和意图类型生成智能执行计划
 * 2. 使用 AI 分析用户需求，生成最优的工具调用序列
 * 3. 支持动态参数解析和上下文关联
 * 4. 提供执行时间估算和性能优化
 *
 * 核心特性：
 * - AI 驱动的计划生成：使用豆包 AI 分析用户意图，生成智能计划
 * - 工具自动选择：从工具注册表中智能选择最合适的工具组合
 * - 参数智能推导：支持静态参数和动态参数的自动解析
 * - 执行优化：预估执行时间，优化步骤顺序和资源使用
 *
 * 技术架构：
 * - 基于 OpenAI SDK 与豆包 AI 进行交互
 * - 采用 JSON 格式的结构化计划描述
 * - 集成工具注册表进行工具配置管理
 * - 支持计划的序列化和持久化存储
 *
 * 设计模式：
 * - 策略模式：不同意图类型对应不同的生成策略
 * - 工厂模式：动态创建执行步骤和参数配置
 * - 建造者模式：逐步构建复杂的执行计划对象
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

const OpenAI = require('openai')
const { doubaoParams, TOOL_REGISTRY } = require('./config')
const { createLogger } = require('./utils')

// SimpleExecutionPlanner 已被移除，因为不再使用
// 现在统一使用 IntelligentExecutionPlanner 进行计划生成
// 这样可以确保所有计划都具备智能分析能力

/**
 * 智能执行计划生成器
 * 使用 AI 生成更复杂和智能的执行计划
 *
 * 核心能力：
 * - 自然语言理解：深度分析用户输入的语义和意图
 * - 智能工具选择：根据任务需求自动选择最优工具组合
 * - 参数智能推导：自动推导工具调用所需的参数
 * - 执行优化：优化步骤顺序，提高执行效率
 *
 * 生成流程：
 * 1. 构建 AI 分析提示词
 * 2. 调用豆包 AI 进行意图分析和计划生成
 * 3. 解析 AI 返回的 JSON 格式计划
 * 4. 构建标准化的执行步骤对象
 * 5. 计算执行时间和依赖关系
 */
class IntelligentExecutionPlanner {
  /**
   * 生成执行计划
   * 这是计划生成器的核心方法，使用 AI 分析用户意图并生成智能执行计划
   *
   * @param {string} userInput - 用户输入内容，用于 AI 分析和参数推导
   * @param {string} intentType - 意图类型：task
   * @returns {Object} 完整的执行计划对象
   *
   * 执行计划对象结构：
   * - planId: 计划唯一标识（UUID）
   * - userInput: 用户原始输入
   * - intentType: 识别的意图类型
   * - steps: 执行步骤数组
   * - totalSteps: 总步骤数
   * - status: 计划状态（pending/running/completed/failed）
   * - startTime: 计划创建时间戳
   * - estimatedTotalTime: 预估总执行时间（毫秒）
   *
   * 生成流程：
   * 1. 初始化执行计划基础结构
   * 2. 构建 AI 分析提示词
   * 3. 调用豆包 AI 进行智能分析
   * 4. 解析 AI 返回的 JSON 计划数据
   * 5. 构建标准化的执行步骤对象
   * 6. 计算总执行时间和步骤数量
   */
  static async generatePlan(userInput, intentType) {
    const logger = createLogger('planner')

    logger.enter('generatePlan', {
      userInputLength: userInput ? userInput.length : 0,
      intentType,
      userInputPreview: userInput ? userInput.substring(0, 100) : '',
    })

    // 初始化执行计划的基础结构
    logger.step('初始化执行计划基础结构')
    const executionPlan = {
      planId: this.generateUUID(), // 生成唯一的计划标识
      userInput: userInput, // 保存用户原始输入，用于后续分析
      intentType: intentType, // 保存意图类型，用于计划分类
      steps: [], // 执行步骤数组，将由 AI 生成填充
      entities: {}, // AI 从用户输入中提取的结构化实体数据
      totalSteps: 0, // 总步骤数，在步骤构建完成后计算
      status: 'pending', // 计划状态，初始为待执行
      startTime: Date.now(), // 计划创建时间戳
      estimatedTotalTime: 0, // 预估总执行时间，在步骤构建完成后计算
    }

    logger.info('执行计划基础结构初始化完成', {
      planId: executionPlan.planId,
      startTime: executionPlan.startTime,
    })

    // 使用 AI 生成更智能的执行计划
    // 构建专门的分析提示词，指导 AI 理解用户意图并生成合适的计划
    logger.step('构建 AI 分析提示词', { intentType })
    const analysisPrompt = this.buildAnalysisPrompt(userInput, intentType)
    logger.info('AI 分析提示词构建完成', { promptLength: analysisPrompt.length })

    try {
      // 调用豆包 AI 进行智能分析，获取结构化的计划数据
      logger.step('调用豆包 AI 进行智能分析')
      const aiResponse = await this.callAI(analysisPrompt)
      logger.info('AI 响应获取成功', { responseLength: aiResponse.length })

      logger.step('解析 AI 返回的 JSON 计划数据')
      const planData = JSON.parse(aiResponse) // 解析 AI 返回的 JSON 格式计划
      logger.info('AI 计划数据解析成功', {
        hasSteps: !!planData.steps,
        stepsCount: planData.steps ? planData.steps.length : 0,
        hasEntities: !!planData.entities,
        entitiesCount: planData.entities ? Object.keys(planData.entities).length : 0,
      })

      // 保存 AI 提取的实体
      if (planData.entities) {
        executionPlan.entities = planData.entities
        logger.info('AI 提取的实体保存完成', {
          entityKeys: Object.keys(planData.entities),
        })
      }

      // 构建执行步骤
      // 遍历 AI 生成的步骤数据，构建标准化的执行步骤对象
      logger.step('开始构建执行步骤', { stepsCount: planData.steps.length })

      if (planData.steps && Array.isArray(planData.steps)) {
        for (let i = 0; i < planData.steps.length; i++) {
          const stepData = planData.steps[i] // AI 生成的步骤数据
          const toolConfig = TOOL_REGISTRY[stepData.toolName] // 从工具注册表获取工具配置

          logger.debug(`构建步骤 ${i + 1}`, {
            stepIndex: i,
            toolName: stepData.toolName,
            hasToolConfig: !!toolConfig,
            description: stepData.description,
          })

          // 构建标准化的执行步骤对象
          const step = {
            stepId: this.generateUUID(), // 生成唯一的步骤标识
            toolName: stepData.toolName, // 要调用的工具名称，必须在工具注册表中存在
            description: stepData.description, // 步骤描述，说明该步骤的作用和目的
            parameters: stepData.parameters, // 工具调用参数，可能包含静态值和动态引用
            dependencies: stepData.dependencies || [], // 步骤依赖关系，指定前置步骤的 stepId
            status: 'pending', // 步骤状态：pending/running/completed/failed
            retryCount: 0, // 当前重试次数，用于错误恢复
            maxRetries: 3, // 最大重试次数，超过后标记为失败
            executionTime: null, // 实际执行时间，执行完成后填充
            estimatedTime: toolConfig?.metadata?.estimatedTime || 2000, // 预估执行时间（毫秒）
            error: null, // 错误信息，执行失败时填充
          }

          // 将构建好的步骤添加到执行计划中
          executionPlan.steps.push(step)
          // 累加预估总执行时间，用于进度预测
          executionPlan.estimatedTotalTime += step.estimatedTime
        }
      }

      // 设置总步骤数，用于进度计算
      logger.step('设置总步骤数和完成计划生成')
      executionPlan.totalSteps = executionPlan.steps.length

      logger.info('执行计划生成完成', {
        planId: executionPlan.planId,
        totalSteps: executionPlan.totalSteps,
        estimatedTotalTime: executionPlan.estimatedTotalTime,
        hasEntities: Object.keys(executionPlan.entities).length > 0,
      })

      logger.exit('generatePlan', executionPlan)
      return executionPlan
    } catch (error) {
      // 错误处理：AI 计划解析失败时的降级策略
      logger.error('AI 计划生成失败，使用默认计划', error, {
        userInputLength: userInput ? userInput.length : 0,
        intentType,
        errorName: error.name,
        errorMessage: error.message,
      })

      // 使用默认计划生成器作为备选方案，确保系统可用性
      const defaultPlan = this.generateDefaultPlan(userInput, intentType)
      logger.info('默认计划生成完成', {
        planId: defaultPlan.planId,
        totalSteps: defaultPlan.totalSteps,
      })

      logger.exit('generatePlan', defaultPlan)
      return defaultPlan
    }
  }

  /**
   * 构建 AI 分析提示词
   * 为豆包 AI 构建专门的分析提示词，指导其生成结构化的执行计划和实体
   *
   * @param {string} userInput - 用户输入内容
   * @param {string} intentType - 意图类型
   * @returns {string} 完整的 AI 分析提示词
   *
   * 提示词结构：
   * 1. 角色定义：定义 AI 的角色和职责
   * 2. 工具说明：提供可用工具的详细信息
   * 3. 任务要求：明确分析任务和输出格式（包含 steps 和 entities）
   * 4. 示例说明：提供标准的输出格式示例
   *
   * 设计原则：
   * - 明确的角色定位，确保 AI 理解其职责
   * - 详细的工具信息，帮助 AI 做出正确选择
   * - 标准化的输出格式，便于后续解析
   * - 丰富的示例，提高生成质量
   */
  static buildAnalysisPrompt(userInput, intentType) {
    // 生成工具信息提示词，让 AI 了解可用的工具和参数
    const toolPrompt = this.generateToolPrompt(TOOL_REGISTRY)

    return `分析用户输入："${userInput}"
      意图类型：${intentType}

      ${toolPrompt}

      请分析用户意图，提取关键实体，并生成执行计划。

      ## 动态参数引用规则：
      - $context.key: 引用上下文数据
      - $step.stepId.data.字段名：引用前置步骤返回的具体字段
      - $plan.entities.key: 引用从用户输入中提取的实体
      - $filter(stepId.data, condition): 对步骤返回的数组数据进行筛选

      ## 重要提示：
      1. 每个工具的返回值都包含 errCode、errMsg 和 data 三个字段
      2. 实际的业务数据在 data 字段中，引用时必须使用 $step.stepId.data.字段名
      3. 对于返回数组的工具（如 getTasks、getProjects），可以使用数组索引或筛选条件
      4. 引用示例：
         - 引用创建任务的 ID: $step.createTaskStep.data.id
         - 引用任务列表第一个任务：$step.getTasksStep.data[0].title
         - 引用项目列表中特定项目：$step.getProjectsStep.data[name="工作项目"].id

      ## 步骤依赖关系：
      - 如果步骤 B 需要使用步骤 A 的返回结果，则在步骤 B 的 dependencies 中添加步骤 A 的 stepId
      - 确保依赖关系正确，避免循环依赖

      返回 JSON 格式：
      {
        "analysis": "对用户意图的详细分析，说明你如何理解这个任务。",
        "entities": {
          "projectName": "提取到的项目名称，如果没有则为 null",
          "taskTitle": "提取到的任务标题，如果没有则为 null",
          "dueDate": "提取到的日期，ISO 8601 格式，如果没有则为 null",
          "priority": "提取到的优先级数字，如果没有则为 null",
          "content": "提取到的任务内容，如果没有则为 null"
        },
        "steps": [
          {
            "toolName": "工具名称（必须是上述工具列表中的一个）",
            "description": "步骤描述，说明该步骤的作用和目的",
            "parameters": {
              "param1": "静态值或动态引用",
              "param2": "$plan.entities.taskTitle",
              "param3": "$step.previousStepId.data.id"
            },
            "dependencies": ["前置步骤的 stepId"],
            "reasoning": "解释为什么选择这个工具、如何设置参数，以及如何使用返回值。"
          }
        ]
      }`
  }

  static generateToolPrompt(toolRegistry) {
    let prompt = '可用工具列表：\n'
    for (const [toolName, config] of Object.entries(toolRegistry)) {
      prompt += `- ${toolName}: ${config.description}\n`
      prompt += `  输入参数：${JSON.stringify(config.parameters)}\n`

      // 从工具注册表中获取返回值结构说明
      if (config.returns) {
        prompt += `  返回值结构：${JSON.stringify(config.returns, null, 2)}\n`
        prompt += `  引用示例：使用 $step.stepId.data.字段名 来引用返回的数据\n`
      }
      prompt += '\n'
    }
    return prompt
  }

  static async callAI(prompt) {
    // 调用豆包 AI 生成执行计划
    const openai = new OpenAI(doubaoParams)
    const response = await openai.chat.completions.create({
      messages: [
        { role: 'system', content: '你是一个专业的任务执行计划生成器。' },
        { role: 'user', content: prompt },
      ],
      model: 'doubao-seed-1-6-flash-250715',
      stream: false,
      thinking: { type: 'disabled' },
      timeout: 30000,
    })

    return response.choices[0].message.content
  }

  static generateDefaultPlan(userInput, intentType) {
    // 当 AI 生成失败时的默认计划
    const executionPlan = {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now(),
      estimatedTotalTime: 0,
    }

    // 基于规则的默认计划生成
    if (intentType === 'task') {
      // 为 task 类型生成通用的默认计划
      // 具体的任务类型将由执行计划生成器根据用户输入智能判断
      const stepId = this.generateUUID()
      executionPlan.steps = [
        {
          stepId: stepId,
          toolName: 'getTasks', // 默认先获取任务列表作为上下文
          description: '获取任务上下文信息',
          parameters: { limit: 10 },
          dependencies: [],
          status: 'pending',
          estimatedTime: 2000,
        },
      ]
      executionPlan.estimatedTotalTime = 2000
    }

    executionPlan.totalSteps = executionPlan.steps.length
    return executionPlan
  }

  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0
      const v = c == 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }
}

module.exports = {
  IntelligentExecutionPlanner, // 统一使用智能执行计划生成器
}
