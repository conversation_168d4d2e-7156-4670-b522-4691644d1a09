/**
 * 通用工具函数模块
 * 提供项目中常用的辅助方法和工具函数
 * 包含统一的日志记录系统
 */

/**
 * 生成符合 RFC 4122 标准的 UUID v4
 * @returns {string} 生成的 UUID 字符串
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

/**
 * 从输入文本中提取项目关键词
 * 支持中文"项目"和英文"project"后缀的匹配
 * @param {string} input - 输入文本
 * @returns {string} 提取的项目关键词，未找到时返回空字符串
 */
function extractProjectKeyword(input) {
  const matches = input.match(/(\w+)项目|(\w+)project/gi)
  if (matches && matches.length > 0) {
    return matches[0].replace(/项目|project/gi, '')
  }
  return ''
}

/**
 * 创建延迟执行的 Promise
 * @param {number} ms - 延迟时间（毫秒）
 * @returns {Promise<void>} 延迟 Promise
 */
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * 深度克隆对象，支持嵌套对象、数组和日期类型
 * @param {*} obj - 要克隆的对象
 * @returns {*} 深度克隆后的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item))
  }

  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }

  return obj
}

/**
 * 将时间戳格式化为 YYYY-MM-DD HH:mm:ss 格式
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 */
function formatTimestamp(timestamp) {
  const date = new Date(timestamp)
  return date.toISOString().replace('T', ' ').substring(0, 19)
}

/**
 * 计算数值数组的平均值
 * @param {number[]} array - 数值数组
 * @returns {number} 平均值，空数组或非数组时返回 0
 */
function calculateAverage(array) {
  if (!Array.isArray(array) || array.length === 0) {
    return 0
  }
  return array.reduce((sum, val) => sum + val, 0) / array.length
}

/**
 * 安全的 JSON 解析，避免解析异常导致程序崩溃
 * @param {string} jsonString - 要解析的 JSON 字符串
 * @param {*} defaultValue - 解析失败时的默认返回值
 * @returns {*} 解析结果或默认值
 */
function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    return defaultValue
  }
}

/**
 * 检查值是否为空（null、undefined、空字符串、空数组、空对象）
 * @param {*} obj - 要检查的值
 * @returns {boolean} 是否为空
 */
function isEmpty(obj) {
  if (obj === null || obj === undefined) {
    return true
  }

  if (typeof obj === 'string' || Array.isArray(obj)) {
    return obj.length === 0
  }

  if (typeof obj === 'object') {
    return Object.keys(obj).length === 0
  }

  return false
}

/**
 * 安全地去除字符串首尾空格
 * 处理 null/undefined 情况，避免类型错误
 * @param {*} str - 要处理的字符串
 * @returns {string} 处理后的字符串，非字符串类型返回空字符串
 */
function safeTrim(str) {
  if (typeof str !== 'string') {
    return ''
  }
  return str.trim()
}

/**
 * 计算动态引用数量
 * 统计参数对象中的动态引用数量
 *
 * @param {Object} parameters - 参数对象
 * @returns {number} 动态引用数量
 */
function countDynamicReferences(parameters) {
  if (!parameters || typeof parameters !== 'object') return 0

  let count = 0
  const countInValue = (value) => {
    if (typeof value === 'string') {
      // 统计 $context.、$step.、$filter( 等动态引用
      const matches = value.match(/\$(?:context\.|step\.|filter\()/g)
      if (matches) count += matches.length
    } else if (Array.isArray(value)) {
      value.forEach(countInValue)
    } else if (typeof value === 'object' && value !== null) {
      Object.values(value).forEach(countInValue)
    }
  }

  Object.values(parameters).forEach(countInValue)
  return count
}

/**
 * 统一日志记录系统
 * 提供模块化的日志记录功能，支持指定模块和全量模式
 *
 * 功能特性：
 * - 模块化日志：支持按模块名称过滤日志输出
 * - 双模式支持：指定模块模式和全量模式
 * - 详细上下文：记录函数调用、参数、错误堆栈等信息
 * - 性能友好：可配置的日志级别，避免生产环境性能影响
 *
 * 使用方式：
 * const logger = createLogger('chatStreamSSE')
 * logger.info('函数开始执行', { message, channel })
 * logger.error('执行失败', error, { context: 'additional info' })
 */
class UnifiedLogger {
  constructor() {
    // 日志配置
    this.config = {
      // 日志模式：'specific' | 'all'
      mode: 'all',

      // 指定模块模式：只打印这些模块的日志
      specificModules: [
        'chatStreamSSE',
        // 'planner',
        // 'executor',
        // 'performance',
        // 'context',
        // 'resolver',
        // 'validator',
        // 'todo',
        // 'error-handler'
      ],

      // 日志级别：'debug' | 'info' | 'warn' | 'error'
      level: 'debug',

      // 是否启用日志（生产环境可设为 false）
      enabled: true,

      // 是否显示堆栈信息
      showStack: true,

      // 最大日志条数（防止内存溢出）
      maxLogs: 1000,
    }

    // 日志存储
    this.logs = []

    // 日志级别优先级
    this.levelPriority = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3,
    }
  }

  /**
   * 创建模块日志记录器
   * @param {string} moduleName - 模块名称
   * @returns {Object} 日志记录器实例
   */
  createLogger(moduleName) {
    return {
      debug: (message, data, context) => this.log('debug', moduleName, message, data, context),
      info: (message, data, context) => this.log('info', moduleName, message, data, context),
      warn: (message, data, context) => this.log('warn', moduleName, message, data, context),
      error: (message, error, context) => this.log('error', moduleName, message, error, context),

      // 特殊方法：记录函数入口
      enter: (functionName, params) => {
        this.log('info', moduleName, `🚀 ${functionName} - 函数入口`, params, { type: 'function_enter' })
      },

      // 特殊方法：记录函数出口
      exit: (functionName, result) => {
        this.log('info', moduleName, `✅ ${functionName} - 函数出口`, result, { type: 'function_exit' })
      },

      // 特殊方法：记录函数异常出口
      exitError: (functionName, error) => {
        this.log('error', moduleName, `❌ ${functionName} - 异常出口`, error, { type: 'function_error' })
      },

      // 特殊方法：记录关键步骤
      step: (stepName, data) => {
        this.log('info', moduleName, `📍 ${stepName}`, data, { type: 'step' })
      },
    }
  }

  /**
   * 核心日志记录方法
   * @param {string} level - 日志级别
   * @param {string} moduleName - 模块名称
   * @param {string} message - 日志消息
   * @param {any} data - 日志数据
   * @param {Object} context - 上下文信息
   */
  log(level, moduleName, message, data, context = {}) {
    // 检查是否启用日志
    if (!this.config.enabled) return

    // 检查日志级别
    if (this.levelPriority[level] < this.levelPriority[this.config.level]) return

    // 检查模块过滤
    if (this.config.mode === 'specific' && !this.config.specificModules.includes(moduleName)) {
      return
    }

    // 创建日志条目
    const logEntry = {
      timestamp: Date.now(),
      level: level.toUpperCase(),
      module: moduleName,
      message,
      data: this.sanitizeData(data),
      context,
      stack: this.config.showStack && level === 'error' ? this.getStackTrace() : null,
    }

    // 存储日志
    this.logs.push(logEntry)

    // 控制日志数量
    if (this.logs.length > this.config.maxLogs) {
      this.logs.shift()
    }

    // 输出到控制台
    this.outputToConsole(logEntry)
  }

  /**
   * 数据清理，避免循环引用和敏感信息
   * @param {any} data - 原始数据
   * @returns {any} 清理后的数据
   */
  sanitizeData(data) {
    if (!data) return data

    try {
      // 处理错误对象
      if (data instanceof Error) {
        return {
          name: data.name,
          message: data.message,
          stack: data.stack,
        }
      }

      // 深度克隆并清理敏感信息
      const sanitized = this.deepCloneWithLimit(data, 3)

      // 清理敏感字段
      return this.removeSensitiveFields(sanitized)
    } catch (error) {
      return { error: '数据序列化失败', original: String(data) }
    }
  }

  /**
   * 有限深度的深度克隆
   * @param {any} obj - 要克隆的对象
   * @param {number} maxDepth - 最大深度
   * @returns {any} 克隆结果
   */
  deepCloneWithLimit(obj, maxDepth) {
    if (maxDepth <= 0 || obj === null || typeof obj !== 'object') {
      return obj
    }

    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) {
      return obj.slice(0, 10).map((item) => this.deepCloneWithLimit(item, maxDepth - 1))
    }

    const result = {}
    let count = 0
    for (const key in obj) {
      if (obj.hasOwnProperty(key) && count < 20) {
        // 限制属性数量
        result[key] = this.deepCloneWithLimit(obj[key], maxDepth - 1)
        count++
      }
    }
    return result
  }

  /**
   * 移除敏感字段
   * @param {any} obj - 对象
   * @returns {any} 清理后的对象
   */
  removeSensitiveFields(obj) {
    if (!obj || typeof obj !== 'object') return obj

    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth']
    const result = { ...obj }

    for (const field of sensitiveFields) {
      if (result[field]) {
        result[field] = '***HIDDEN***'
      }
    }

    return result
  }

  /**
   * 获取堆栈跟踪
   * @returns {string} 堆栈信息
   */
  getStackTrace() {
    try {
      throw new Error()
    } catch (error) {
      return error.stack
    }
  }

  /**
   * 输出到控制台
   * @param {Object} logEntry - 日志条目
   */
  outputToConsole(logEntry) {
    const timestamp = new Date(logEntry.timestamp).toISOString()
    const prefix = `[${timestamp}] [${logEntry.level}] [${logEntry.module}]`

    // 根据级别选择控制台方法
    const consoleMethod =
      {
        DEBUG: 'log',
        INFO: 'info',
        WARN: 'warn',
        ERROR: 'error',
      }[logEntry.level] || 'log'

    // 输出基本信息
    console[consoleMethod](`${prefix} ${logEntry.message}`)

    // 输出数据
    if (logEntry.data !== undefined) {
      console[consoleMethod](`${prefix} 数据:`, logEntry.data)
    }

    // 输出上下文
    if (logEntry.context && Object.keys(logEntry.context).length > 0) {
      console[consoleMethod](`${prefix} 上下文:`, logEntry.context)
    }

    // 输出堆栈
    if (logEntry.stack) {
      console[consoleMethod](`${prefix} 堆栈:`, logEntry.stack)
    }
  }

  /**
   * 更新日志配置
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取日志历史
   * @param {Object} filter - 过滤条件
   * @returns {Array} 日志列表
   */
  getLogs(filter = {}) {
    let logs = [...this.logs]

    if (filter.module) {
      logs = logs.filter((log) => log.module === filter.module)
    }

    if (filter.level) {
      logs = logs.filter((log) => log.level === filter.level.toUpperCase())
    }

    if (filter.since) {
      logs = logs.filter((log) => log.timestamp >= filter.since)
    }

    return logs
  }

  /**
   * 清空日志
   */
  clearLogs() {
    this.logs = []
  }

  /**
   * 生成日志报告
   * @returns {Object} 日志统计报告
   */
  generateReport() {
    const report = {
      totalLogs: this.logs.length,
      byLevel: {},
      byModule: {},
      timeRange: {
        start: this.logs.length > 0 ? this.logs[0].timestamp : null,
        end: this.logs.length > 0 ? this.logs[this.logs.length - 1].timestamp : null,
      },
    }

    this.logs.forEach((log) => {
      // 按级别统计
      report.byLevel[log.level] = (report.byLevel[log.level] || 0) + 1

      // 按模块统计
      report.byModule[log.module] = (report.byModule[log.module] || 0) + 1
    })

    return report
  }
}

// 创建全局日志实例
const globalLogger = new UnifiedLogger()

/**
 * 创建模块日志记录器的便捷函数
 * @param {string} moduleName - 模块名称
 * @returns {Object} 日志记录器实例
 */
function createLogger(moduleName) {
  return globalLogger.createLogger(moduleName)
}

module.exports = {
  generateUUID,
  extractProjectKeyword,
  delay,
  deepClone,
  formatTimestamp,
  calculateAverage,
  safeJsonParse,
  isEmpty,
  safeTrim,
  countDynamicReferences,

  // 日志系统
  UnifiedLogger,
  globalLogger,
  createLogger,
}
