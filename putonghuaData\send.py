import json
import os
import glob
from typing import Dict, Any, List
import time
import requests
from requests.exceptions import RequestException, Timeout, ConnectionError

class PutonghuaServerAPI:
    def __init__(self):
        self.base_url = "https://fc-mp-c6815a6a-de20-45ad-a345-2e804f127311.next.bspapp.com/lifeOS"
        self.api_key = "zcjjj"  # API 密钥
        self.timeout = 30  # 请求超时时间（秒）

    def send_batch_data(self, data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量发送普通话数据到服务器"""
        url = f"{self.base_url}/batchUpdatePutonghuaData"

        headers = {
            'Content-Type': 'application/json',
            'X-API-KEY': self.api_key,
            'User-Agent': 'PutonghuaDataSender/1.0'
        }

        payload = {
            'dataList': data_list
        }

        try:
            print(f"正在发送 {len(data_list)} 条数据到服务器...")
            print(f"请求 URL: {url}")

            response = requests.post(
                url=url,
                json=payload,
                headers=headers,
                timeout=self.timeout
            )

            # 检查 HTTP 状态码
            if response.status_code == 200:
                result = response.json()
                return {
                    'success': True,
                    'data': result,
                    'status_code': response.status_code
                }
            else:
                # 尝试解析错误响应
                try:
                    error_data = response.json()
                    error_message = error_data.get('message', f'HTTP {response.status_code} 错误')
                except:
                    error_message = f'HTTP {response.status_code} 错误'

                return {
                    'success': False,
                    'error': error_message,
                    'status_code': response.status_code,
                    'response_text': response.text[:500]  # 限制错误响应长度
                }

        except Timeout:
            return {
                'success': False,
                'error': f'请求超时（超过 {self.timeout} 秒）',
                'status_code': None
            }
        except ConnectionError:
            return {
                'success': False,
                'error': '网络连接错误，请检查网络连接',
                'status_code': None
            }
        except RequestException as e:
            return {
                'success': False,
                'error': f'网络请求异常：{str(e)}',
                'status_code': None
            }
        except json.JSONDecodeError:
            return {
                'success': False,
                'error': '服务器返回的数据格式错误（非 JSON 格式）',
                'status_code': response.status_code if 'response' in locals() else None
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'未知错误：{str(e)}',
                'status_code': None
            }

    def print_result_summary(self, result: Dict[str, Any], total_files: int):
        """打印操作结果摘要"""
        print("\n" + "="*60)
        print("📊 批量更新结果摘要")
        print("="*60)

        if result['success']:
            data = result['data']
            print(f"✅ 请求状态：成功 (HTTP {result['status_code']})")
            print(f"📁 处理文件数：{total_files}")
            print(f"📝 总记录数：{data['data']['totalCount']}")
            print(f"✅ 成功更新：{data['data']['successCount']} 条")
            print(f"❌ 更新失败：{data['data']['errorCount']} 条")

            # 显示错误详情（如果有）
            if data['data']['errorCount'] > 0 and data['data'].get('errors'):
                print(f"\n⚠️  错误详情：")
                for i, error in enumerate(data['data']['errors'][:5], 1):
                    print(f"   {i}. {error}")
                if len(data['data']['errors']) > 5:
                    print(f"   ... 还有 {len(data['data']['errors']) - 5} 个错误")

            # 判断整体结果
            if data['data']['errorCount'] == 0:
                print(f"\n🎉 所有数据更新成功！")
            else:
                print(f"\n⚠️  部分数据更新失败，请检查错误详情")
        else:
            print(f"❌ 请求状态：失败")
            if result.get('status_code'):
                print(f"📡 HTTP 状态码：{result['status_code']}")
            print(f"💬 错误信息：{result['error']}")
            if result.get('response_text'):
                print(f"📄 服务器响应：{result['response_text']}")

        print("="*60)

def get_json_files(directory: str = ".") -> list:
    """获取指定目录下的所有 JSON 文件"""
    pattern = os.path.join(directory, "*.json")
    return glob.glob(pattern)

def read_json_file(file_path: str) -> Dict[str, Any]:
    """读取 JSON 文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except json.JSONDecodeError as e:
        print(f"❌ JSON 格式错误 {file_path}: {e}")
        return {}
    except FileNotFoundError:
        print(f"❌ 文件不存在：{file_path}")
        return {}
    except Exception as e:
        print(f"❌ 读取文件 {file_path} 时出错：{e}")
        return {}

def validate_putonghua_data(data: Dict[str, Any], filename: str) -> bool:
    """验证普通话数据格式"""
    required_fields = ['_id', 'character', 'category', 'words']

    for field in required_fields:
        if field not in data:
            print(f"⚠️  {filename}: 缺少必要字段 '{field}'")
            return False

    if not isinstance(data['words'], list):
        print(f"⚠️  {filename}: 'words' 字段必须是数组")
        return False

    return True

def process_json_files():
    """处理所有 JSON 文件并批量发送到服务器"""
    api = PutonghuaServerAPI()

    # 获取当前目录下的所有 JSON 文件
    json_files = get_json_files()

    if not json_files:
        print("❌ 当前目录下没有找到 JSON 文件")
        return

    print(f"🔍 找到 {len(json_files)} 个 JSON 文件：")
    for file in json_files:
        print(f"   📄 {os.path.basename(file)}")

    print(f"\n📖 开始读取文件...")

    # 收集所有有效的数据
    all_data = []
    successful_files = []
    failed_files = []

    # 遍历并读取每个 JSON 文件
    for file_path in json_files:
        filename = os.path.basename(file_path)
        print(f"   正在处理：{filename}", end=" ... ")

        # 读取 JSON 文件内容
        data = read_json_file(file_path)

        if data:
            # 验证数据格式
            if validate_putonghua_data(data, filename):
                all_data.append(data)
                successful_files.append(filename)
                print("✅ 成功")
            else:
                failed_files.append(filename)
                print("❌ 格式错误")
        else:
            failed_files.append(filename)
            print("❌ 读取失败")

    # 显示读取结果
    print(f"\n📊 文件读取结果：")
    print(f"   ✅ 成功读取：{len(successful_files)} 个文件")
    print(f"   ❌ 读取失败：{len(failed_files)} 个文件")

    if failed_files:
        print(f"   失败文件：{', '.join(failed_files)}")

    if not all_data:
        print("\n❌ 没有有效的数据可以发送")
        return

    # 显示数据预览
    print(f"\n📋 数据预览（前 3 条）:")
    for i, data in enumerate(all_data[:3], 1):
        print(f"   {i}. {data['character']} ({data['category']}) - {len(data['words'])} 个词语")

    if len(all_data) > 3:
        print(f"   ... 还有 {len(all_data) - 3} 条数据")

    # 询问用户是否继续
    print(f"\n🚀 准备发送 {len(all_data)} 条普通话数据到服务器")
    print(f"📡 目标服务器：{api.base_url}")
    user_input = input("是否继续？(y/N): ").strip().lower()

    if user_input not in ['y', 'yes', '是']:
        print("❌ 用户取消操作")
        return

    # 批量发送数据
    print(f"\n📡 开始批量发送数据...")
    start_time = time.time()

    result = api.send_batch_data(all_data)

    end_time = time.time()
    elapsed_time = end_time - start_time

    # 显示结果
    api.print_result_summary(result, len(successful_files))
    print(f"⏱️  总耗时：{elapsed_time:.2f} 秒")

    # 根据结果给出建议
    if result['success']:
        if result['data']['data']['errorCount'] == 0:
            print(f"\n🎊 恭喜！所有数据都已成功更新到服务器")
        else:
            print(f"\n💡 建议：检查失败的记录，修复后重新发送")
    else:
        print(f"\n💡 建议：检查网络连接和服务器状态，稍后重试")

def check_dependencies():
    """检查必要的依赖是否已安装"""
    try:
        import requests
        return True
    except ImportError:
        print("❌ 缺少必要的依赖包 'requests'")
        print("💡 请运行以下命令安装：pip install requests")
        return False

def main():
    """主函数"""
    print("🎯 普通话数据批量发送工具")
    print("="*50)

    # 检查依赖
    if not check_dependencies():
        return

    try:
        process_json_files()
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错：{e}")
        print("💡 请检查错误信息并重试")

if __name__ == "__main__":
    main()