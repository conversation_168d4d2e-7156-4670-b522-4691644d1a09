<template>
  <div v-if="loading.show" class="loading-bubble" :class="`stage-${loading.stage}`">
    <div class="loading-content">
      <!-- 加载动画 - 使用 Font Awesome 图标 -->
      <div class="loading-icon">
        <i class="fas fa-brain"></i>
      </div>

      <!-- 加载文案 -->
      <div class="loading-text">{{ loading.text }}</div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  loading: {
    type: Object,
    required: true,
  },
})
</script>

<style lang="scss" scoped>
.loading-bubble {
  max-width: 70%;
  border-radius: 12px;
  padding: 12px 16px;
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  margin-bottom: 12px;

  &.stage-thinking {
    border-left: 3px solid var(--color-primary);
  }

  &.stage-analyzing {
    border-left: 3px solid var(--color-warning);
  }

  &.stage-executing {
    border-left: 3px solid var(--color-success);
  }

  &.stage-generating {
    border-left: 3px solid var(--color-info);
  }

  &.stage-preparing {
    border-left: 3px solid var(--color-secondary);
  }

  .loading-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    min-height: 20px;
  }

  .loading-icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;

    i {
      font-size: 16px;
      color: var(--color-primary);
      animation: breathing 2s ease-in-out infinite;
    }
  }

  .loading-text {
    font-size: 14px;
    color: var(--color-gray-600);
    flex: 1;
    min-width: 0; /* 允许文本收缩 */
    word-wrap: break-word;
    word-break: break-all;
    line-height: 1.4;
    max-height: 4.2em; /* 限制最多显示 3 行 */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
  }
}

@keyframes breathing {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}
</style>
