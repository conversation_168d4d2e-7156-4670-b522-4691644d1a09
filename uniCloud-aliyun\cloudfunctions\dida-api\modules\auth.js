/**
 * 认证模块
 * 处理微信登录、密码登录等认证相关功能
 */

const {
	WECHAT_URLS,
	WECHAT_CONFIG,
	DIDA_API_BASE,
	DIDA_AUTH_APIS,
	REQUEST_CONFIG,
	buildWeChatQRUrl,
	buildWeChatPollUrl,
	buildDidaApiUrl,
	buildWeChatValidateUrl,
	buildPasswordLoginUrl,
} = require("../config");

const {
	logInfo,
	logError,
	createSuccessResponse,
	createErrorResponse,
	validateRequired,
	validateType,
	validateEmail,
	buildAuthHeaders,
	buildAuthCookies,
	safeJsonParse,
} = require("../utils");

/**
 * 获取微信登录二维码
 * @param {string} state - 状态参数，默认为配置中的值
 * @returns {object} 包含二维码 URL 和密钥的响应
 */
async function getWeChatQRCode(state = null) {
	const methodName = "getWeChatQRCode";
	logInfo(methodName, "开始获取微信登录二维码", { state });

	try {
		// 使用默认状态参数
		if (state === null) {
			state = WECHAT_CONFIG.default_state;
		}

		// 构建微信登录二维码 URL
		const qrCodeUrl = buildWeChatQRUrl(state);

		// 发起 HTTP 请求获取二维码页面
		const response = await uniCloud.httpclient.request(qrCodeUrl, {
			method: "GET",
			headers: {
				"User-Agent": REQUEST_CONFIG.user_agent,
				Accept:
					"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
				"Accept-Language":
					"zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
				"Accept-Encoding": "gzip, deflate, br",
				Connection: "keep-alive",
				"Upgrade-Insecure-Requests": "1",
			},
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`获取二维码页面失败，状态码：${response.status}`);
		}

		// 解析 HTML 页面，提取二维码密钥
		const htmlContent = response.data;
		const uuidMatch = htmlContent.match(/uuid=([^"&]+)/);

		if (!uuidMatch) {
			throw new Error("无法从页面中提取二维码密钥");
		}

		const qrCodeKey = uuidMatch[1];

		// 构建二维码图片 URL
		const qrImageUrl = `${WECHAT_URLS.qr_image_base_url}/${qrCodeKey}`;

		logInfo(methodName, "成功获取微信登录二维码", { qrCodeKey, qrImageUrl });

		return createSuccessResponse("获取二维码成功", {
			qr_code_url: qrImageUrl,
			qr_code_key: qrCodeKey,
			state: state,
			login_url: qrCodeUrl,
		});
	} catch (error) {
		logError(methodName, "获取微信登录二维码失败", error);
		throw error;
	}
}

/**
 * 轮询二维码状态
 * @param {string} qrCodeKey - 二维码密钥
 * @param {number} maxAttempts - 最大轮询次数，默认 60 次
 * @returns {object} 登录结果
 */
async function pollQRStatus(qrCodeKey, maxAttempts = 60) {
	const methodName = "pollQRStatus";
	logInfo(methodName, "开始轮询二维码状态", { qrCodeKey, maxAttempts });

	try {
		// 参数验证
		const keyValidation = validateRequired(qrCodeKey, "qrCodeKey");
		if (keyValidation) return keyValidation;

		const attemptsValidation = validateType(
			maxAttempts,
			"number",
			"maxAttempts"
		);
		if (attemptsValidation) return attemptsValidation;

		let attempts = 0;

		while (attempts < maxAttempts) {
			attempts++;

			try {
				// 构建轮询URL
				const pollUrl = buildWeChatPollUrl(qrCodeKey);

				logInfo(methodName, `第${attempts}次轮询`, { pollUrl });

				// 发起轮询请求
				const response = await uniCloud.httpclient.request(pollUrl, {
					method: "GET",
					headers: {
						"User-Agent": REQUEST_CONFIG.user_agent,
						Accept: "*/*",
						"Accept-Language":
							"zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
						"Accept-Encoding": "gzip, deflate, br",
						Connection: "keep-alive",
						Referer: "https://open.weixin.qq.com/",
					},
					timeout: 10000, // 轮询请求使用较短的超时时间
				});

				if (response.status === 200) {
					const responseText = response.data;

					// 解析响应内容
					if (responseText.includes("window.wx_errcode=405")) {
						// 二维码已过期
						logInfo(methodName, "二维码已过期");
						return createSuccessResponse("二维码已过期", {
							success: false,
							status: "expired",
							message: "二维码已过期，请重新获取",
						});
					} else if (responseText.includes("window.wx_errcode=404")) {
						// 等待用户扫码
						logInfo(methodName, `第${attempts}次轮询：等待用户扫码`);

						// 等待2秒后继续轮询
						await new Promise((resolve) => setTimeout(resolve, 2000));
						continue;
					} else if (responseText.includes("window.wx_errcode=403")) {
						// 用户取消授权
						logInfo(methodName, "用户取消授权");
						return createSuccessResponse("用户取消授权", {
							success: false,
							status: "cancelled",
							message: "用户取消了授权",
						});
					} else if (responseText.includes("window.wx_code=")) {
						// 登录成功，提取授权码
						const codeMatch = responseText.match(/window\.wx_code='([^']+)'/);
						const stateMatch = responseText.match(/window\.wx_state='([^']+)'/);

						if (codeMatch) {
							const code = codeMatch[1];
							const state = stateMatch
								? stateMatch[1]
								: WECHAT_CONFIG.default_state;

							logInfo(methodName, "用户扫码成功，获取到授权码", {
								code,
								state,
							});

							return createSuccessResponse("扫码成功", {
								success: true,
								status: "success",
								message: "扫码成功",
								code: code,
								state: state,
							});
						}
					}
				}

				// 等待 2 秒后继续轮询
				await new Promise((resolve) => setTimeout(resolve, 2000));
			} catch (pollError) {
				logError(methodName, `第${attempts}次轮询失败`, pollError);

				// 如果是网络错误，继续轮询
				if (pollError.code === "TIMEOUT" || pollError.code === "ENOTFOUND") {
					await new Promise((resolve) => setTimeout(resolve, 2000));
					continue;
				}

				// 其他错误直接抛出
				throw pollError;
			}
		}

		// 轮询超时
		logInfo(methodName, "轮询超时");
		return createSuccessResponse("轮询超时", {
			success: false,
			status: "timeout",
			message: "轮询超时，请重新获取二维码",
		});
	} catch (error) {
		logError(methodName, "轮询二维码状态失败", error);
		throw error;
	}
}

/**
 * 验证微信登录
 * @param {string} code - 扫码后获得的验证码
 * @param {string} state - 状态参数
 * @returns {object} 验证响应
 */
async function validateWeChatLogin(code, state = null) {
	const methodName = "validateWeChatLogin";
	logInfo(methodName, "开始验证微信登录", { code, state });

	try {
		// 参数验证
		const codeValidation = validateRequired(code, "code");
		if (codeValidation) return codeValidation;

		// 使用默认状态参数
		if (state === null) {
			state = WECHAT_CONFIG.default_state;
		}

		// 构建验证 URL
		const validateUrl = buildWeChatValidateUrl(code, state);

		logInfo(methodName, "发起微信登录验证请求", { validateUrl });

		// 发起验证请求
		const response = await uniCloud.httpclient.request(validateUrl, {
			method: "GET",
			headers: {
				"User-Agent": REQUEST_CONFIG.user_agent,
				Accept: "application/json, text/plain, */*",
				"Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
				"Accept-Encoding": "gzip, deflate, br",
				Connection: "keep-alive",
				Origin: "https://dida365.com",
				Referer: "https://dida365.com/",
			},
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`微信登录验证失败，状态码：${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析验证响应数据");
		}

		logInfo(methodName, "微信登录验证响应", responseData);

		// 检查验证结果
		if (responseData.success || responseData.token) {
			// 登录成功
			return createSuccessResponse("微信登录验证成功", {
				success: true,
				message: "登录成功",
				token: responseData.token,
				user_info: responseData.user || null,
				csrf_token: responseData.csrfToken || null,
			});
		} else {
			// 登录失败
			return createSuccessResponse("微信登录验证失败", {
				success: false,
				message: responseData.message || "验证失败",
				error_code: responseData.errorCode || null,
			});
		}
	} catch (error) {
		logError(methodName, "验证微信登录失败", error);
		throw error;
	}
}

/**
 * 密码登录
 * @param {string} username - 登录账户（邮箱或手机号）
 * @param {string} password - 登录密码
 * @returns {object} 登录结果
 */
async function passwordLogin(username, password) {
	const methodName = "passwordLogin";
	logInfo(methodName, "开始密码登录", { username });

	try {
		// 参数验证
		const usernameValidation = validateRequired(username, "username");
		if (usernameValidation) return usernameValidation;

		const passwordValidation = validateRequired(password, "password");
		if (passwordValidation) return passwordValidation;

		// 如果用户名包含@符号，验证邮箱格式
		if (username.includes("@")) {
			const emailValidation = validateEmail(username, "username");
			if (emailValidation) return emailValidation;
		}

		// 构建登录 URL
		const loginUrl = buildPasswordLoginUrl(true, true);

		// 准备登录数据
		const loginData = {
			phone: username,
			password: password,
		};

		logInfo(methodName, "发起密码登录请求", { loginUrl, username });
		console.log(REQUEST_CONFIG.device_info);
		// 发起登录请求
		const response = await uniCloud.httpclient.request(loginUrl, {
			method: "POST",
			headers: {
				"X-Device": REQUEST_CONFIG.device_info,
				"User-Agent": REQUEST_CONFIG.user_agent,
				Accept: "application/json, text/plain, */*",
				"Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
				"Accept-Encoding": "gzip, deflate, br",
				Connection: "keep-alive",
				"Content-Type": "application/json",
				Origin: "https://dida365.com",
				Referer: "https://dida365.com/",
			},
			data: JSON.stringify(loginData),
			timeout: REQUEST_CONFIG.timeout,
		});

		if (response.status !== 200) {
			throw new Error(`密码登录失败，状态码：${response.status}`);
		}

		// 解析响应数据
		const responseData = safeJsonParse(response.data, null);

		if (!responseData) {
			throw new Error("无法解析登录响应数据");
		}

		// 提取 cookies（从响应头中）
		let cookies = {};
		if (response.headers && response.headers["set-cookie"]) {
			const setCookieHeader = response.headers["set-cookie"];
			console.log(setCookieHeader)
			// 解析 Set-Cookie 头
			const cookieMatches = setCookieHeader.match(/([^=]+)=([^;]+)/g);
			if (cookieMatches) {
				cookieMatches.forEach((match) => {
					const [name, value] = match.split("=");
					if (name && value) {
						cookies[name.trim()] = value.trim();
					}
				});
			}
		}

		// 提取认证令牌和 CSRF 令牌
		const token = responseData.token || cookies.t || null;
		const csrfToken = cookies._csrf_token || null;

		logInfo(methodName, "密码登录响应", {
			...responseData,
			extractedCookies: cookies,
			csrfToken: csrfToken ? `${csrfToken.substring(0, 10)}...` : "null",
		});

		// 检查登录结果
		if (responseData.success || token) {
			// 登录成功
			return createSuccessResponse("密码登录成功", {
				success: true,
				message: "登录成功",
				token: token,
				user_info: responseData.user || null,
				csrf_token: csrfToken,
				cookies: cookies,
			});
		} else {
			// 登录失败
			return createSuccessResponse("密码登录失败", {
				success: false,
				message: responseData.message || "登录失败",
				error_code: responseData.errorCode || null,
			});
		}
	} catch (error) {
		logError(methodName, "密码登录失败", error);
		throw error;
	}
}

// 导出认证模块函数
module.exports = {
	getWeChatQRCode,
	pollQRStatus,
	validateWeChatLogin,
	passwordLogin,
};
