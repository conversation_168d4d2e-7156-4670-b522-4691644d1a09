/**
 * AI助手聊天界面测试
 * 测试消息发送后的加载状态显示功能
 */

import { describe, it, expect, beforeEach } from '@jest/globals'

// Mock uniCloud
global.uniCloud = {
  importObject: jest.fn(() => ({
    chatStreamSSE: jest.fn(),
  })),
  SSEChannel: jest.fn(() => ({
    on: jest.fn(),
    open: jest.fn(),
    close: jest.fn(),
  })),
}

// Mock uni API
global.uni = {
  createInnerAudioContext: jest.fn(() => ({
    src: '',
    onEnded: jest.fn(),
    onError: jest.fn(),
    onTimeUpdate: jest.fn(),
    play: jest.fn(),
    pause: jest.fn(),
    stop: jest.fn(),
    destroy: jest.fn(),
  })),
  $emit: jest.fn(),
  $on: jest.fn(),
  $off: jest.fn(),
}

// 模拟AI助手的核心逻辑
class MockAiAssistant {
  constructor() {
    this.messages = [
      {
        _id: '1',
        content: '你好！我是你的 AI 助手，有什么可以帮助你的吗？',
        type: 'text',
        isUser: false,
        time: new Date().toISOString(),
      },
    ]
    this.inputValue = ''
    this.isStreaming = false
    this.currentStreamingMessageId = null
  }

  // 模拟添加消息的方法
  addMessage(message) {
    this.messages.push({
      ...message,
      _id: Date.now().toString(),
      time: new Date().toISOString(),
    })
  }

  // 模拟处理流式消息的方法
  handleStreamMessage(data) {
    switch (data.type) {
      case 'start':
        // 移除加载状态消息
        this.messages = this.messages.filter((msg) => !msg.loading)

        const startMessage = {
          _id: Date.now().toString(),
          content: '',
          type: 'text',
          isUser: false,
          streaming: true,
          time: new Date().toISOString(),
        }
        this.messages.push(startMessage)
        this.currentStreamingMessageId = startMessage._id
        this.isStreaming = true
        break

      case 'error':
        // 移除加载状态消息
        this.messages = this.messages.filter((msg) => !msg.loading)

        if (this.currentStreamingMessageId) {
          const messageIndex = this.messages.findIndex((msg) => msg._id === this.currentStreamingMessageId)
          if (messageIndex !== -1) {
            this.messages[messageIndex].content = `抱歉，发生了错误：${data.error}`
            this.messages[messageIndex].streaming = false
          }
        } else {
          // 如果没有正在流式传输的消息，直接添加错误消息
          this.addMessage({
            content: `抱歉，发生了错误：${data.error}`,
            type: 'text',
            isUser: false,
          })
        }
        this.isStreaming = false
        this.currentStreamingMessageId = null
        break

      case 'task_complete':
        // 处理任务执行完成
        if (this.currentStreamingMessageId) {
          const messageIndex = this.messages.findIndex((msg) => msg._id === this.currentStreamingMessageId)
          if (messageIndex !== -1) {
            this.messages[messageIndex].content = data.content || '任务执行完成'
            this.messages[messageIndex].streaming = false
          }
        }
        this.isStreaming = false
        this.currentStreamingMessageId = null
        break
    }
  }

  // 模拟发送消息的方法
  async handleSendMessageStream() {
    if (!this.inputValue.trim() || this.isStreaming) return

    // 添加用户消息
    this.addMessage({
      content: this.inputValue,
      type: 'text',
      isUser: true,
    })

    const userMessage = this.inputValue
    this.inputValue = ''

    // 立即添加AI思考中的加载状态消息
    const loadingMessage = {
      _id: `loading_${Date.now()}`,
      content: '',
      type: 'text',
      isUser: false,
      loading: true,
      time: new Date().toISOString(),
    }
    this.messages.push(loadingMessage)
  }
}

describe('AI助手聊天界面', () => {
  let aiAssistant

  beforeEach(() => {
    aiAssistant = new MockAiAssistant()
  })

  it('应该在发送消息后立即显示加载状态', async () => {
    // 设置输入值
    aiAssistant.inputValue = '测试消息'

    // 获取初始消息数量
    const initialMessageCount = aiAssistant.messages.length

    // 触发发送消息
    await aiAssistant.handleSendMessageStream()

    // 验证消息数组中增加了两条消息：用户消息和加载消息
    expect(aiAssistant.messages.length).toBe(initialMessageCount + 2)

    // 验证最后一条消息是加载状态消息
    const lastMessage = aiAssistant.messages[aiAssistant.messages.length - 1]
    expect(lastMessage.loading).toBe(true)
    expect(lastMessage.isUser).toBe(false)
    expect(lastMessage.type).toBe('text')
    expect(lastMessage._id).toMatch(/^loading_/)
  })

  it('应该在AI开始回复时移除加载状态消息', async () => {
    // 先添加一个加载消息
    aiAssistant.messages.push({
      _id: 'loading_test',
      content: '',
      type: 'text',
      isUser: false,
      loading: true,
      time: new Date().toISOString(),
    })

    const messageCountWithLoading = aiAssistant.messages.length

    // 模拟AI开始回复
    aiAssistant.handleStreamMessage({
      type: 'start',
    })

    // 验证加载消息被移除
    const hasLoadingMessage = aiAssistant.messages.some((msg) => msg.loading)
    expect(hasLoadingMessage).toBe(false)

    // 验证添加了新的流式消息
    const lastMessage = aiAssistant.messages[aiAssistant.messages.length - 1]
    expect(lastMessage.streaming).toBe(true)
    expect(lastMessage.isUser).toBe(false)
  })

  it('应该在发生错误时移除加载状态消息并显示错误信息', async () => {
    // 先添加一个加载消息
    aiAssistant.messages.push({
      _id: 'loading_test',
      content: '',
      type: 'text',
      isUser: false,
      loading: true,
      time: new Date().toISOString(),
    })

    // 模拟错误发生
    aiAssistant.handleStreamMessage({
      type: 'error',
      error: '测试错误',
    })

    // 验证加载消息被移除
    const hasLoadingMessage = aiAssistant.messages.some((msg) => msg.loading)
    expect(hasLoadingMessage).toBe(false)

    // 验证添加了错误消息
    const lastMessage = aiAssistant.messages[aiAssistant.messages.length - 1]
    expect(lastMessage.content).toContain('测试错误')
    expect(lastMessage.isUser).toBe(false)
  })

  it('应该正确过滤历史消息，排除加载和流式消息', async () => {
    // 设置测试消息数组
    aiAssistant.messages = [
      { content: '用户消息1', isUser: true, type: 'text' },
      { content: 'AI回复1', isUser: false, type: 'text' },
      { content: '', isUser: false, type: 'text', loading: true },
      { content: 'AI回复2', isUser: false, type: 'text', streaming: true },
      { content: '用户消息2', isUser: true, type: 'text' },
    ]

    // 设置输入值并发送消息
    aiAssistant.inputValue = '新消息'
    await aiAssistant.handleSendMessageStream()

    // 验证历史消息过滤正确（应该只包含非loading、非streaming的文本消息）
    // 验证消息数量增加了用户消息和加载消息
    expect(aiAssistant.messages.length).toBe(7) // 原有5条 + 用户消息 + 加载消息

    // 验证最后一条是加载消息
    const lastMessage = aiAssistant.messages[aiAssistant.messages.length - 1]
    expect(lastMessage.loading).toBe(true)
  })

  it('应该正确处理任务完成消息', async () => {
    // 先添加一个流式消息
    aiAssistant.messages.push({
      _id: 'streaming_test',
      content: '',
      type: 'text',
      isUser: false,
      streaming: true,
      time: new Date().toISOString(),
    })
    aiAssistant.currentStreamingMessageId = 'streaming_test'
    aiAssistant.isStreaming = true

    // 模拟任务完成消息
    aiAssistant.handleStreamMessage({
      type: 'task_complete',
      content: '任务已成功创建',
    })

    // 验证流式状态被重置
    expect(aiAssistant.isStreaming).toBe(false)
    expect(aiAssistant.currentStreamingMessageId).toBe(null)

    // 验证消息内容被更新
    const lastMessage = aiAssistant.messages[aiAssistant.messages.length - 1]
    expect(lastMessage.content).toBe('任务已成功创建')
    expect(lastMessage.streaming).toBe(false)
  })
})
